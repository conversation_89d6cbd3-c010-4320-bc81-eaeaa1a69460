package main

import (
	"bufio"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"regexp"
	"strings"
	"time"
)

// Struct untuk parsing JSON response
type ProxyResponse struct {
	Providers map[string]Provider `json:"providers"`
}

type Provider struct {
	Name        string  `json:"name"`
	Type        string  `json:"type"`
	VehicleType string  `json:"vehicleType"`
	Proxies     []Proxy `json:"proxies"`
}

type Proxy struct {
	Name    string    `json:"name"`
	Type    string    `json:"type"`
	Alive   bool      `json:"alive"`
	History []History `json:"history"`
}

type History struct {
	Time  string `json:"time"`
	Delay int    `json:"delay"`
}

// Fungsi untuk mendapatkan secret dari konfigurasi mihomo
func getMihomoSecret() (string, error) {
	// Coba cari proses mihomo yang sedang berjalan
	cmd := exec.Command("ps", "w")
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("failed to run ps command: %v", err)
	}

	// Cari baris yang mengandung mihomo
	lines := strings.Split(string(output), "\n")
	var configFile string
	for _, line := range lines {
		if strings.Contains(line, "mihomo") && strings.Contains(line, "-f") {
			// Extract config file path
			re := regexp.MustCompile(`-f\s+(\S+)`)
			matches := re.FindStringSubmatch(line)
			if len(matches) > 1 {
				configFile = matches[1]
				break
			}
		}
	}

	if configFile == "" {
		return "", fmt.Errorf("mihomo process not found or config file not specified")
	}

	// Baca file konfigurasi
	file, err := os.Open(configFile)
	if err != nil {
		return "", fmt.Errorf("failed to open config file %s: %v", configFile, err)
	}
	defer file.Close()

	// Scan file untuk mencari secret
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if strings.HasPrefix(line, "secret:") {
			// Extract secret value
			parts := strings.SplitN(line, ":", 2)
			if len(parts) == 2 {
				secret := strings.TrimSpace(parts[1])
				if secret == "" {
					return "", fmt.Errorf("secret value is empty in config file")
				}
				return secret, nil
			}
		}
	}

	return "", fmt.Errorf("secret not found in config file %s", configFile)
}

// Fungsi untuk mendapatkan data proxy dari API
func getProxyData() (*ProxyResponse, error) {
	// Create client with timeout
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	// Create request
	url := "http://192.168.1.1:9090/providers/proxies/"
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("error creating request: %v", err)
	}

	// Get secret from mihomo config
	secret, err := getMihomoSecret()
	if err != nil {
		return nil, fmt.Errorf("error getting mihomo secret: %v", err)
	}

	// Add authorization header
	req.Header.Add("Authorization", "Bearer "+secret)

	// Send request
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error sending request: %v", err)
	}
	defer resp.Body.Close()

	// Read response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response: %v", err)
	}

	// Check status code
	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("error: Status Code %d, Response: %s", resp.StatusCode, string(body))
	}

	// Parse JSON response
	var proxyResponse ProxyResponse
	err = json.Unmarshal(body, &proxyResponse)
	if err != nil {
		return nil, fmt.Errorf("error parsing JSON: %v", err)
	}

	return &proxyResponse, nil
}

// Command: ping - Menampilkan status ping semua proxy
func cmdPing() {
	proxyResponse, err := getProxyData()
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}

	fmt.Println("=== PROXY PING STATUS ===")
	fmt.Println()

	for providerName, provider := range proxyResponse.Providers {
		// Filter hanya HTTP atau File vehicleType
		if provider.VehicleType == "HTTP" || provider.VehicleType == "File" {
			fmt.Printf("Provider: %s (Type: %s)\n", providerName, provider.VehicleType)

			for _, proxy := range provider.Proxies {
				// Ambil ping terbaru dari history
				latestPing := "N/A"
				status := "❌ Dead"

				if proxy.Alive {
					status = "✅ Alive"
				}

				if len(proxy.History) > 0 {
					latestDelay := proxy.History[len(proxy.History)-1].Delay
					if latestDelay == 0 {
						latestPing = "0ms (timeout/error)"
					} else {
						latestPing = fmt.Sprintf("%dms", latestDelay)
					}
				}

				fmt.Printf("  - %s [%s] | Ping: %s | Status: %s\n",
					proxy.Name, proxy.Type, latestPing, status)
			}
			fmt.Println()
		}
	}
}

// Command: tunnel - Menampilkan daftar proxy yang bisa digunakan untuk tunnel
func cmdTunnel() {
	proxyResponse, err := getProxyData()
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}

	fmt.Println("=== AVAILABLE TUNNELS ===")
	fmt.Println()

	aliveCount := 0
	for providerName, provider := range proxyResponse.Providers {
		// Filter hanya HTTP atau File vehicleType
		if provider.VehicleType == "HTTP" || provider.VehicleType == "File" {
			fmt.Printf("Provider: %s (Type: %s)\n", providerName, provider.VehicleType)

			for _, proxy := range provider.Proxies {
				if proxy.Alive {
					aliveCount++
					latestPing := "N/A"
					if len(proxy.History) > 0 {
						latestDelay := proxy.History[len(proxy.History)-1].Delay
						if latestDelay == 0 {
							latestPing = "0ms"
						} else {
							latestPing = fmt.Sprintf("%dms", latestDelay)
						}
					}
					fmt.Printf("  ✅ %s [%s] | Ping: %s\n", proxy.Name, proxy.Type, latestPing)
				}
			}
			fmt.Println()
		}
	}
	fmt.Printf("Total available tunnels: %d\n", aliveCount)
}

// Command: config - Menampilkan informasi konfigurasi mihomo
func cmdConfig() {
	secret, err := getMihomoSecret()
	if err != nil {
		fmt.Printf("Error getting mihomo config: %v\n", err)
		return
	}

	// Cari proses mihomo untuk mendapatkan info config file
	cmd := exec.Command("ps", "w")
	output, err := cmd.Output()
	if err != nil {
		fmt.Printf("Error running ps command: %v\n", err)
		return
	}

	lines := strings.Split(string(output), "\n")
	var configFile string
	for _, line := range lines {
		if strings.Contains(line, "mihomo") && strings.Contains(line, "-f") {
			re := regexp.MustCompile(`-f\s+(\S+)`)
			matches := re.FindStringSubmatch(line)
			if len(matches) > 1 {
				configFile = matches[1]
				break
			}
		}
	}

	fmt.Println("=== MIHOMO CONFIGURATION ===")
	fmt.Println()
	fmt.Printf("Config File: %s\n", configFile)
	fmt.Printf("Secret: %s\n", secret)
	fmt.Printf("API Endpoint: http://192.168.1.1:9090\n")
	fmt.Printf("Authorization: Bearer %s\n", secret)
}

// Fungsi untuk menampilkan help
func showHelp() {
	fmt.Println("Usage: go run main.go [command]")
	fmt.Println()
	fmt.Println("Available commands:")
	fmt.Println("  ping    - Show ping status of all proxies")
	fmt.Println("  tunnel  - Show available tunnels (alive proxies only)")
	fmt.Println("  config  - Show mihomo configuration info")
	fmt.Println("  help    - Show this help message")
	fmt.Println()
	fmt.Println("Examples:")
	fmt.Println("  go run main.go ping")
	fmt.Println("  go run main.go tunnel")
	fmt.Println("  go run main.go config")
	fmt.Println("  ./main ping")
	fmt.Println("  ./main tunnel")
	fmt.Println("  ./main config")
}

func main() {
	// Check command line arguments
	if len(os.Args) < 2 {
		fmt.Println("Error: No command specified")
		fmt.Println()
		showHelp()
		return
	}

	command := os.Args[1]

	switch command {
	case "ping":
		cmdPing()
	case "tunnel":
		cmdTunnel()
	case "help", "-h", "--help":
		showHelp()
	default:
		fmt.Printf("Error: Unknown command '%s'\n", command)
		fmt.Println()
		showHelp()
	}
}
