package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"
)

// Struct untuk parsing JSON response
type ProxyResponse struct {
	Providers map[string]Provider `json:"providers"`
}

type Provider struct {
	Name        string  `json:"name"`
	Type        string  `json:"type"`
	VehicleType string  `json:"vehicleType"`
	Proxies     []Proxy `json:"proxies"`
}

type Proxy struct {
	Name    string    `json:"name"`
	Type    string    `json:"type"`
	Alive   bool      `json:"alive"`
	History []History `json:"history"`
}

type History struct {
	Time  string `json:"time"`
	Delay int    `json:"delay"`
}

func main() {
	// Create client with timeout
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	// Create request
	url := "http://192.168.1.1:9090/providers/proxies/"
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		fmt.Printf("Error creating request: %v\n", err)
		return
	}

	// Add authorization header
	req.Header.Add("Authorization", "Bearer aqila22")

	// Send request
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("Error sending request: %v\n", err)
		return
	}
	defer resp.Body.Close()

	// Read response
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("Error reading response: %v\n", err)
		return
	}

	// Check status code
	if resp.StatusCode != 200 {
		fmt.Printf("Error: Status Code %d\n", resp.StatusCode)
		fmt.Printf("Response Body: %s\n", string(body))
		return
	}

	// Parse JSON response
	var proxyResponse ProxyResponse
	err = json.Unmarshal(body, &proxyResponse)
	if err != nil {
		fmt.Printf("Error parsing JSON: %v\n", err)
		return
	}

	// Filter dan tampilkan proxy providers dengan vehicleType HTTP atau File
	fmt.Println("=== PROXY PROVIDERS ===")
	fmt.Println()

	for providerName, provider := range proxyResponse.Providers {
		// Filter hanya HTTP atau File vehicleType
		if provider.VehicleType == "HTTP" || provider.VehicleType == "File" {
			fmt.Printf("Provider: %s (Type: %s)\n", providerName, provider.VehicleType)
			fmt.Println("Proxies:")

			for _, proxy := range provider.Proxies {
				// Ambil ping terbaru dari history
				latestPing := "N/A"
				status := "❌ Dead"

				if proxy.Alive {
					status = "✅ Alive"
				}

				if len(proxy.History) > 0 {
					latestDelay := proxy.History[len(proxy.History)-1].Delay
					if latestDelay == 0 {
						latestPing = "0ms (timeout/error)"
					} else {
						latestPing = fmt.Sprintf("%dms", latestDelay)
					}
				}

				fmt.Printf("  - %s [%s] | Ping: %s | Status: %s\n",
					proxy.Name, proxy.Type, latestPing, status)
			}
			fmt.Println()
		}
	}
}
